package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.Prompt;
import com.writing.mapper.PromptMapper;
import com.writing.service.PromptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 提示词服务实现类
 */
@Service
@RequiredArgsConstructor
public class PromptServiceImpl extends ServiceImpl<PromptMapper, Prompt> implements PromptService {
    
    @Override
    public List<Prompt> getPromptsByUserId(Long userId, String category) {
        LambdaQueryWrapper<Prompt> queryWrapper = new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getUserId, userId)
                .orderByDesc(Prompt::getUsageCount)
                .orderByDesc(Prompt::getUpdatedAt);
        
        // 如果指定了分类，添加分类筛选
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(Prompt::getCategory, category);
        }
        
        return this.list(queryWrapper);
    }
    
    @Override
    public Prompt getPromptById(Long promptId, Long userId) {
        return this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
    }
    
    @Override
    public Prompt createPrompt(Prompt prompt) {
        // 设置默认值
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(0); // 0表示非默认，1表示默认
        }
        if (prompt.getUsageCount() == null) {
            prompt.setUsageCount(0);
        }
        
        this.save(prompt);
        return prompt;
    }
    
    @Override
    public Prompt updatePrompt(Prompt prompt, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt existingPrompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, prompt.getId())
                .eq(Prompt::getUserId, userId));
        
        if (existingPrompt == null) {
            throw new RuntimeException("提示词不存在或无权限修改");
        }
        
        // 保留原有的使用次数和默认状态
        prompt.setUsageCount(existingPrompt.getUsageCount());
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(existingPrompt.getIsDefault());
        }
        
        this.updateById(prompt);
        return prompt;
    }
    
    @Override
    public boolean deletePrompt(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限删除");
        }
        
        // 不允许删除默认提示词
        if (prompt.getIsDefault() != null && prompt.getIsDefault() == 1) {
            throw new RuntimeException("不能删除默认提示词");
        }
        
        return this.removeById(promptId);
    }
    
    @Override
    public void incrementUsageCount(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限访问");
        }
        
        // 增加使用次数
        prompt.setUsageCount(prompt.getUsageCount() + 1);
        this.updateById(prompt);
    }
    
    @Override
    public void initDefaultPrompts(Long userId) {
        // 检查是否已经初始化过
        long count = this.count(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getUserId, userId)
                .eq(Prompt::getIsDefault, 1));

        if (count > 0) {
            return; // 已经初始化过
        }

        // 创建默认提示词
        List<Prompt> defaultPrompts = getDefaultPrompts();
        for (Prompt prompt : defaultPrompts) {
            prompt.setUserId(userId);
            this.save(prompt);
        }
    }

    @Override
    public List<Prompt> getDefaultPrompts() {
        List<Prompt> defaultPrompts = new ArrayList<>();

        // 1. 小说大纲生成器
        Prompt outlineGenerator = new Prompt();
        outlineGenerator.setTitle("小说大纲生成器");
        outlineGenerator.setCategory("outline");
        outlineGenerator.setDescription("根据关键词和类型生成详细的小说大纲");
        outlineGenerator.setContent("请为我创作一个{类型}小说的大纲，主题是{主题}，主角是{主角设定}。要求包含：\n1. 故事背景设定\n2. 主要人物介绍\n3. 核心冲突\n4. 章节大纲（至少10章）\n5. 结局走向");
        outlineGenerator.setTags(Arrays.asList("大纲", "结构", "创作"));
        outlineGenerator.setIsDefault(1);
        outlineGenerator.setUsageCount(0);
        defaultPrompts.add(outlineGenerator);

        // 2. 基础章节生成器
        Prompt basicChapterGenerator = new Prompt();
        basicChapterGenerator.setTitle("基础章节生成器");
        basicChapterGenerator.setCategory("content");
        basicChapterGenerator.setDescription("基于章节大纲生成详细的正文内容");
        basicChapterGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容。\n\n章节大纲：{章节大纲}\n\n要求：\n1. 字数控制在{目标字数}字左右\n2. 采用{写作视角}视角\n3. 包含丰富的对话、描写和细节\n4. 保持情节连贯性\n5. 突出{重点内容}");
        basicChapterGenerator.setTags(Arrays.asList("正文", "章节", "基础生成"));
        basicChapterGenerator.setIsDefault(1);
        basicChapterGenerator.setUsageCount(0);
        defaultPrompts.add(basicChapterGenerator);

        // 3. 文本润色优化
        Prompt textPolisher = new Prompt();
        textPolisher.setTitle("文本润色优化");
        textPolisher.setCategory("polish");
        textPolisher.setDescription("优化文本的表达和文采，提升阅读体验");
        textPolisher.setContent("请帮我润色以下文本，要求：\n1. 保持原意不变\n2. 提升文采和表达力\n3. 优化句式结构\n4. 增强画面感\n\n原文：{原文内容}");
        textPolisher.setTags(Arrays.asList("润色", "优化", "文采"));
        textPolisher.setIsDefault(1);
        textPolisher.setUsageCount(0);
        defaultPrompts.add(textPolisher);

        // 4. 智能续写助手
        Prompt continueWriter = new Prompt();
        continueWriter.setTitle("智能续写助手");
        continueWriter.setCategory("continue");
        continueWriter.setDescription("基于现有内容进行智能续写");
        continueWriter.setContent("请为小说《{小说标题}》的章节《{章节标题}》续写内容。\n\n当前已写内容：\n{当前内容}\n\n续写要求：\n1. 保持原有风格和语调\n2. 情节自然连贯\n3. 长度约{续写字数}字\n4. 推进剧情发展");
        continueWriter.setTags(Arrays.asList("续写", "连贯", "发展"));
        continueWriter.setIsDefault(1);
        continueWriter.setUsageCount(0);
        defaultPrompts.add(continueWriter);

        // 5. 人物设定生成器
        Prompt characterGenerator = new Prompt();
        characterGenerator.setTitle("人物设定生成器");
        characterGenerator.setCategory("character");
        characterGenerator.setDescription("生成详细的人物设定和背景故事");
        characterGenerator.setContent("请为小说《{小说标题}》创建一个{角色类型}角色，基本信息：\n- 姓名：{姓名}\n- 角色定位：{角色定位}\n- 性别：{性别}\n- 年龄：{年龄}岁\n\n请详细设定：\n1. 外貌特征\n2. 性格特点\n3. 背景故事\n4. 能力特长\n5. 人际关系\n6. 内心动机");
        characterGenerator.setTags(Arrays.asList("人设", "角色", "背景"));
        characterGenerator.setIsDefault(1);
        characterGenerator.setUsageCount(0);
        defaultPrompts.add(characterGenerator);

        // 6. 全素材章节生成器
        Prompt fullMaterialGenerator = new Prompt();
        fullMaterialGenerator.setTitle("全素材章节生成器");
        fullMaterialGenerator.setCategory("content");
        fullMaterialGenerator.setDescription("结合人物、世界观、语料库等素材生成章节内容");
        fullMaterialGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容。\n\n章节大纲：{章节大纲}\n\n{主要人物}\n\n{世界观设定}\n\n{参考语料}\n\n{前文概要}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 采用{写作视角}视角\n3. 突出重点：{重点内容}\n4. 充分运用提供的人物设定和世界观背景\n5. 参考语料库的写作风格和表达方式\n6. 与前文保持连贯性和一致性\n7. 包含丰富的对话、心理活动、环境描写\n8. 情节发展要符合章节大纲要求");
        fullMaterialGenerator.setTags(Arrays.asList("全素材", "章节", "综合生成"));
        fullMaterialGenerator.setIsDefault(1);
        fullMaterialGenerator.setUsageCount(0);
        defaultPrompts.add(fullMaterialGenerator);

        // 7. 对话驱动生成器
        Prompt dialogueGenerator = new Prompt();
        dialogueGenerator.setTitle("对话驱动生成器");
        dialogueGenerator.setCategory("content-dialogue");
        dialogueGenerator.setDescription("以对话为主导的章节内容生成");
        dialogueGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出对话。\n\n章节大纲：{章节大纲}\n参与对话人物：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 对话占60%以上篇幅\n3. 通过对话推进情节发展\n4. 展现人物性格和关系\n5. 适当加入动作和心理描写\n6. 对话要符合人物身份和性格\n7. 重点内容：{重点内容}");
        dialogueGenerator.setTags(Arrays.asList("对话", "人物", "互动"));
        dialogueGenerator.setIsDefault(1);
        dialogueGenerator.setUsageCount(0);
        defaultPrompts.add(dialogueGenerator);

        // 8. 场景描写生成器
        Prompt sceneGenerator = new Prompt();
        sceneGenerator.setTitle("场景描写生成器");
        sceneGenerator.setCategory("content-scene");
        sceneGenerator.setDescription("以环境和场景描写为主的内容生成");
        sceneGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出场景描写。\n\n章节大纲：{章节大纲}\n场景设定：{世界观设定}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 详细描写环境氛围\n3. 通过场景烘托情节\n4. 调动读者五感体验\n5. 场景与情节相辅相成\n6. 体现世界观特色\n7. 重点内容：{重点内容}");
        sceneGenerator.setTags(Arrays.asList("场景", "环境", "氛围"));
        sceneGenerator.setIsDefault(1);
        sceneGenerator.setUsageCount(0);
        defaultPrompts.add(sceneGenerator);

        // 9. 动作剧情生成器
        Prompt actionGenerator = new Prompt();
        actionGenerator.setTitle("动作剧情生成器");
        actionGenerator.setCategory("content-action");
        actionGenerator.setDescription("以动作和情节推进为主的内容生成");
        actionGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出动作情节。\n\n章节大纲：{章节大纲}\n主要人物：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 节奏紧凑，情节推进迅速\n3. 动作描写清晰流畅\n4. 突出冲突和转折\n5. 保持紧张感和悬念\n6. 角色行动符合性格\n7. 重点内容：{重点内容}");
        actionGenerator.setTags(Arrays.asList("动作", "情节", "冲突"));
        actionGenerator.setIsDefault(1);
        actionGenerator.setUsageCount(0);
        defaultPrompts.add(actionGenerator);

        // 10. 心理描写生成器
        Prompt psychologyGenerator = new Prompt();
        psychologyGenerator.setTitle("心理描写生成器");
        psychologyGenerator.setCategory("content-psychology");
        psychologyGenerator.setDescription("以心理活动和内心独白为主的内容生成");
        psychologyGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出心理描写。\n\n章节大纲：{章节大纲}\n主角心境：{重点内容}\n人物背景：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 深入挖掘人物内心世界\n3. 心理活动要真实细腻\n4. 体现人物成长变化\n5. 内心冲突与外在情节呼应\n6. 适当运用意识流技巧\n7. 展现人物独特思维方式");
        psychologyGenerator.setTags(Arrays.asList("心理", "内心", "情感"));
        psychologyGenerator.setIsDefault(1);
        psychologyGenerator.setUsageCount(0);
        defaultPrompts.add(psychologyGenerator);

        return defaultPrompts;
    }
}
