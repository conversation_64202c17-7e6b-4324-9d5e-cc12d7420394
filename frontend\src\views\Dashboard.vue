<template>
  <div class="dashboard-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ 'collapsed': isCollapse }">
      <div class="logo">
        <h2>📚 爱写作</h2>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        @select="handleMenuSelect"
        :collapse="isCollapse"
        :collapse-transition="false"
      >
        <el-menu-item index="/">
          <el-icon><House /></el-icon>
          <template #title>首页</template>
        </el-menu-item>
        
        <el-menu-item index="/novels">
          <el-icon><Document /></el-icon>
          <template #title>小说列表</template>
        </el-menu-item>
        
        <el-menu-item index="/prompts">
          <el-icon><ChatLineSquare /></el-icon>
          <template #title>提示词库</template>
        </el-menu-item>
        
        <el-menu-item index="/genres">
          <el-icon><Collection /></el-icon>
          <template #title>小说类型管理</template>
        </el-menu-item>
        
        <el-menu-item index="/chapters">
          <el-icon><Notebook /></el-icon>
          <template #title>章节管理</template>
        </el-menu-item>
        
        <el-menu-item index="/goals">
          <el-icon><Aim /></el-icon>
          <template #title>写作目标</template>
        </el-menu-item>
        
        <el-menu-item index="/billing">
          <el-icon><CreditCard /></el-icon>
          <template #title>Token计费</template>
        </el-menu-item>
        
        <el-menu-item index="/tools">
          <el-icon><Tools /></el-icon>
          <template #title>工具库</template>
        </el-menu-item>
        
        <el-menu-item index="/short-story">
          <el-icon><EditPen /></el-icon>
          <template #title>短文写作</template>
        </el-menu-item>
        
        <el-menu-item index="/book-analysis">
          <el-icon><DataAnalysis /></el-icon>
          <template #title>拆书工具</template>
        </el-menu-item>
        
        <el-menu-item index="/settings">
          <el-icon><Setting /></el-icon>
          <template #title>系统设置</template>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="header">
        <div class="header-left">
          <el-button 
            type="text" 
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          <span class="page-title">{{ pageTitle }}</span>
        </div>
        
        <div class="header-right">
          <!-- 模型选择 -->
          <div class="model-selector" v-if="isApiConfigured">
            <el-select 
              v-model="currentModel"
              @change="handleModelChange"
              size="small"
              style="width: 220px"
              placeholder="选择模型"
            >
              <!-- 可用模型 -->
              <el-option-group label="⚙️ 可用模型" v-if="availableModels.length > 0">
                <el-option
                  v-for="model in availableModels"
                  :key="model.id"
                  :label="model.name"
                  :value="model.id"
                >
                  <span>{{ model.name }}</span>
                  <span v-if="model.description" style="float: right; color: #8492a6; font-size: 12px">
                    {{ model.description }}
                  </span>
                </el-option>
              </el-option-group>

              <!-- 无可用模型时的提示 -->
              <el-option
                v-if="availableModels.length === 0"
                disabled
                value=""
                label="暂无可用模型，请先创建API配置"
              />
            </el-select>
          </div>

          <!-- API配置状态 -->
          <el-button
            @click="showApiConfig = true"
            :type="isApiConfigured ? 'success' : 'warning'"
            size="small"
          >
            <el-icon><Key /></el-icon>
            {{ isApiConfigured ? 'API已配置' : 'API配置' }}
          </el-button>

          <!-- 用户信息下拉菜单 -->
          <el-dropdown @command="handleCommand" v-if="currentUser">
            <div class="user-dropdown">
              <el-avatar :size="32" :src="currentUser.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ currentUser.nickname || currentUser.username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item disabled>
                  <div class="user-info-detail">
                    <div class="user-name">{{ currentUser.nickname || currentUser.username }}</div>
                    <div class="user-role">{{ getUserRole(currentUser) }}</div>
                  </div>
                </el-dropdown-item> -->
                <el-dropdown-item divided command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  个人设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 页面内容 -->
      <div class="content">
        <router-view />
      </div>
    </div>
    
    <!-- API配置对话框 -->
    <el-dialog v-model="showApiConfig" title="API配置" width="1000px">
      <ApiConfig @close="showApiConfig = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNovelStore } from '@/stores/novel'
import {
  House, Document, ChatLineSquare, Collection, Notebook, Aim,
  CreditCard, Setting, Key, Tools, EditPen, DataAnalysis,
  Expand, Fold, User, SwitchButton, ArrowDown
} from '@element-plus/icons-vue'
import ApiConfig from '@/components/ApiConfig.vue'
import { authApi, authUtils } from '@/services/authApi'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const route = useRoute()
const novelStore = useNovelStore()

// 响应式数据
const isCollapse = ref(false)
const showApiConfig = ref(false)
const activeMenu = ref('/')
const currentModel = ref('')
const currentUser = ref(null)

// 计算属性
const isApiConfigured = computed(() => novelStore.isApiConfigured)

// 获取当前API配置
const currentApiConfig = computed(() => {
  return novelStore.getCurrentApiConfig()
})



// 可用模型列表（从后端API配置中获取）
const availableModels = ref([])

// 加载可用模型列表
const loadAvailableModels = async () => {
  try {
    // 检查用户是否已登录
    const token = localStorage.getItem('token')
    if (!token) {
      console.log('用户未登录，无法加载后端模型配置')
      availableModels.value = []
      return
    }

    // 从后端获取所有启用的API配置
    const { apiConfigApi } = await import('@/services/apiConfigApi.js')
    const configs = await apiConfigApi.getApiConfigs()

    if (!configs || configs.length === 0) {
      console.log('暂无API配置')
      availableModels.value = []
      return
    }

    const enabledConfigs = configs.filter(config => config.enabled && config.selectedModel)

    // 提取所有可用的模型
    const modelSet = new Set()
    const modelDetails = new Map()

    // 只添加配置中的模型
    enabledConfigs.forEach(config => {
      if (config.selectedModel) {
        modelSet.add(config.selectedModel)
        modelDetails.set(config.selectedModel, {
          id: config.selectedModel,
          name: config.selectedModel,
          description: `来自配置: ${config.name}`
        })
      }
    })

    availableModels.value = Array.from(modelSet).map(id => modelDetails.get(id))
    console.log('加载的可用模型:', availableModels.value)
  } catch (error) {
    console.error('加载可用模型失败:', error)
    // 如果加载失败，设置为空数组
    availableModels.value = []
  }
}



const pageTitle = computed(() => {
  const titleMap = {
    '/': '首页',
    '/novels': '小说列表',
    '/prompts': '提示词库',
    '/genres': '小说类型管理',
    '/chapters': '章节管理',
    '/goals': '写作目标',
    '/billing': 'Token计费',
    '/tools': '工具库',
    '/short-story': '短文写作',
    '/book-analysis': '拆书工具',
    '/settings': '系统设置'
  }
  return titleMap[route.path] || '首页'
})



// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleMenuSelect = (index) => {
  router.push(index)
}

// 用户相关功能
const loadUserInfo = () => {
  try {
    const user = authUtils.getUser()
    if (user) {
      currentUser.value = user
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}





// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      handleProfile()
      break
    case 'settings':
      handleSettings()
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleProfile = () => {
  // 跳转到个人中心页面
  router.push('/profile')
}

const handleSettings = () => {
  // 跳转到个人设置页面
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'logout-confirm-dialog'
      }
    )

    // 显示加载状态
    const loading = ElMessage({
      message: '正在退出...',
      type: 'info',
      duration: 0
    })

    try {
      // 调用注销API
      await authApi.logout()

      // 清除用户信息
      currentUser.value = null

      loading.close()
      ElMessage.success('退出成功')

      // 跳转到登录页面
      router.push('/login')

    } catch (apiError) {
      loading.close()
      console.error('注销API调用失败:', apiError)

      // 即使API调用失败，也要清除本地数据
      authApi.logout() // 这个方法只清除本地存储
      currentUser.value = null

      ElMessage.success('退出成功')
      router.push('/login')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      ElMessage.error('退出登录失败')
    }
  }
}

// 模型相关功能
const handleModelChange = async (modelId) => {
  try {
    console.log('切换模型:', modelId)

    // 查找包含此模型的API配置
    const { apiConfigApi } = await import('@/services/apiConfigApi.js')
    const configs = await apiConfigApi.getApiConfigs()
    const configWithModel = configs.find(config =>
      config.selectedModel === modelId
    )

    if (configWithModel) {
      // 1. 首先确保配置是启用状态
      if (!configWithModel.enabled) {
        const enableConfigData = {
          ...configWithModel,
          enabled: 1
        }
        await apiConfigApi.updateApiConfig(configWithModel.id, enableConfigData)
        console.log('已启用配置:', configWithModel.name)
      }

      // 2. 设置为默认配置
      await apiConfigApi.setDefaultApiConfig(configWithModel.id)
      console.log('已设为默认配置:', configWithModel.name)

      // 3. 切换到此配置
      const success = await novelStore.switchToApiConfig(configWithModel.id)

      if (success) {
        currentModel.value = modelId
        const modelName = getModelDisplayName(modelId)
        ElMessage.success(`已切换到模型: ${modelName}，并设为默认配置`)
      } else {
        ElMessage.error('配置设置成功，但切换失败')
      }
    } else {
      // 如果没有找到对应的配置，提示用户创建配置
      const modelName = getModelDisplayName(modelId)
      ElMessage.warning(`模型 ${modelName} 需要先创建对应的API配置`)
    }

  } catch (error) {
    console.error('切换模型失败:', error)
    ElMessage.error('切换模型失败: ' + error.message)
  }
}

const getModelDisplayName = (modelId) => {
  // 在可用模型中查找
  const model = availableModels.value.find(m => m.id === modelId)
  if (model) return model.name

  // 找不到就返回原ID
  return modelId
}

// 初始化模型选择器
const initializeModelSelector = async () => {
  try {
    // 加载可用模型列表
    await loadAvailableModels()

    // 获取当前选中的模型
    try {
      // 直接从API服务获取当前配置
      const { apiConfigApi } = await import('@/services/apiConfigApi.js')
      const defaultConfig = await apiConfigApi.getDefaultApiConfig()

      if (defaultConfig && defaultConfig.selectedModel) {
        currentModel.value = defaultConfig.selectedModel
        console.log('从默认配置获取到模型:', currentModel.value)
      } else if (currentApiConfig.value && currentApiConfig.value.selectedModel) {
        // 回退到计算属性
        currentModel.value = currentApiConfig.value.selectedModel
        console.log('从计算属性获取到模型:', currentModel.value)
      } else {
        console.log('未找到默认模型')
      }
    } catch (error) {
      console.error('获取默认配置失败:', error)
      // 回退到计算属性
      if (isApiConfigured.value && currentApiConfig.value) {
        currentModel.value = currentApiConfig.value.selectedModel || ''
      }
    }

    console.log('模型选择器初始化完成, 当前模型:', currentModel.value)
  } catch (error) {
    console.error('初始化模型选择器失败:', error)
  }
}

// 监听路由变化
watch(() => route.path, (newPath) => {
  activeMenu.value = newPath
}, { immediate: true })

// 监听API配置变化，更新模型选择器
watch(() => [isApiConfigured.value, currentApiConfig.value], async () => {
  // 延迟一下，确保配置已经加载完成
  setTimeout(async () => {
    await initializeModelSelector()
  }, 100)
}, { immediate: true })

// 组件挂载时初始化
onMounted(async () => {
  // 确保store已经初始化完成
  await novelStore.initializeApiConfig()

  // 延迟一下再初始化模型选择器
  setTimeout(async () => {
    await initializeModelSelector()
  }, 200)

  loadUserInfo()
})

</script>

<style scoped>
.dashboard-container {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background-color: #304156;
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar.collapsed .logo h2 {
  display: none;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  margin: 0;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
  white-space: nowrap;
}

.sidebar-menu {
  border: none;
  background-color: #304156;
  height: calc(100vh - 60px);
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  color: #bfcbd9;
  border-bottom: none;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background-color: #263445;
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 15px;
  font-size: 18px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.model-selector {
  display: flex;
  align-items: center;
}

.model-selector .el-select {
  min-width: 200px;
}

.model-selector .el-select .el-input__inner {
  font-size: 13px;
}

/* 模型分组样式 */
.model-selector :deep(.el-select-group__title) {
  font-weight: 600;
  color: #409eff;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.model-selector :deep(.el-option-group .el-option) {
  padding-left: 20px;
}

.model-selector :deep(.el-option-group:not(:last-child)) {
  border-bottom: 1px solid #e4e7ed;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  color: #606266;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.user-dropdown .username {
  font-size: 14px;
  font-weight: 500;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-dropdown .dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 下拉菜单内容样式 */
.user-info-detail {
  padding: 8px 0;
  text-align: center;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 8px;
}

.user-info-detail .user-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.user-info-detail .user-role {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}



/* 下拉菜单项样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

:deep(.el-dropdown-menu__item.is-divided) {
  border-top: 1px solid #ebeef5;
  margin-top: 6px;
}

/* 退出登录确认对话框样式 */
:deep(.logout-confirm-dialog) {
  border-radius: 8px;
}

:deep(.logout-confirm-dialog .el-message-box__header) {
  padding: 20px 20px 10px;
}

:deep(.logout-confirm-dialog .el-message-box__content) {
  padding: 10px 20px;
  color: #606266;
}

:deep(.logout-confirm-dialog .el-message-box__btns) {
  padding: 10px 20px 20px;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }
  
  .main-container {
    margin-left: 0;
  }
  
  .content {
    padding: 15px;
  }
}
</style>